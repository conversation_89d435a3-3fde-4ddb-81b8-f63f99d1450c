import {Box, Input, Text, Button} from "@chakra-ui/react";
import {useColorModeValue} from "@/components/ui/color-mode.tsx";
import {useState, ChangeEvent, FormEvent} from "react";

export const CreatePage = () => {
    const [newProduct, setNewProduct] = useState<{
        name: string;
        price: number;
        imageUrl: string;
    }>({
        "name": "",
        "price": 0,
        "imageUrl": ""
    })

    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        const {name, value} = e.target;
        setNewProduct(prev => ({
            ...prev,
            [name]: name === "price" ? Number(value) : value
        }));
    };

    const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        // Handle form submission
        console.log(newProduct);
    };

    return (
        <>
            <Text
                fontSize={{base: "4xl", md: "5xl"}}
                fontWeight='bold'
                textAlign="center"
                mt={10}
            >
                Create Your Product
            </Text>

            <Box
                as="form"
                mx="auto"
                maxW="500px"
                mt={8}
                p={6}
                bg={useColorModeValue("gray.200", "gray.800")}
                borderRadius="lg"
                boxShadow="base"
            >
                <Input
                    bg={useColorModeValue("white", "black")}
                    name="name"
                    value={newProduct.name}
                    onChange={handleInputChange}
                    placeholder="Product Name"
                    mb={4}
                />
                <Input
                    bg={useColorModeValue("white", "black")}
                    name="price"
                    value={newProduct.price}
                    onChange={handleInputChange}
                    placeholder="Price"
                    type="number"
                    mb={4}
                />
                <Input
                    bg={useColorModeValue("white", "black")}
                    name="imageUrl"
                    value={newProduct.imageUrl}
                    onChange={handleInputChange}
                    placeholder="Image URL"
                    mb={4}
                />
                <Button
                    w="100%"
                    colorScheme=""
                    size="lg"
                    type="submit"
                    onClick={handleSubmit}
                >
                    Create Product
                </Button>
            </Box>
        </>
    )
}